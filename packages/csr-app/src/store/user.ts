import { defineStore } from 'pinia'
import {
  guestSignUp,
  convertGuestToUser,
  type UserConvertParams,
  whoami,
  userPlayedStories,
  updateUserInfo,
  UserInfo,
  userLikedStories,
  LoginWithCodeParams,
  loginWithCode,
} from '@/api/user'
import { Message } from '@/mobile/components/Message'
import axios from 'axios'
import { v4 as uuidv4 } from 'uuid'
import { Story } from '@/api/stories'
import { ReportEvent } from '@/interface'
import { reportEvent, getDeviceId } from '@/utils'
import { pick } from 'lodash-es'
import { setSentryUser, setSentryContext } from '@/utils/sentry'

export interface UserState {
  userId: string | null
  token: string | null
  refreshToken: string | null
  deviceId: string | null
  userInfo: {
    uuid?: string
    name?: string
    email?: string
    avatar_url?: string
    status?: string
    plan?: string
    coins?: number
    gender?: 'male' | 'female' | 'unknown'
    role?: 'guest' | 'normal' | 'admin'
    create_time?: string
  } | null
  isAuthenticated: boolean
  userPlayedStories: Story[]
  userLikedStories: Story[]
}

export const useUserStore = defineStore('user', {
  state: (): UserState => {
    // 尝试从 localStorage 恢复状态
    const token = localStorage.getItem('token')
    const refreshToken = localStorage.getItem('refresh_token')
    const userId = localStorage.getItem('user_id')
    const deviceId = localStorage.getItem('device_id')
    const userInfoStr = localStorage.getItem('userInfo')
    let userInfo = null

    try {
      userInfo = userInfoStr ? JSON.parse(userInfoStr) : null
    } catch (e) {
      console.error('Failed to parse userInfo:', e)
    }

    return {
      userId: userId || null,
      token: token || null,
      refreshToken: refreshToken || null,
      deviceId: deviceId || null,
      userInfo,
      isAuthenticated: !!token && !!userId,
      userPlayedStories: [],
      userLikedStories: [],
    }
  },
  getters: {
    storedToken: () => localStorage.getItem('token'),
    storedRefreshToken: () => localStorage.getItem('refresh_token'),
    storedUserId: () => localStorage.getItem('user_id'),
    storedDeviceId: () => localStorage.getItem('device_id'),
    storedUserInfo: () => {
      const userInfoStr = localStorage.getItem('userInfo')
      return userInfoStr ? JSON.parse(userInfoStr) : null
    },
    isGuest: (state) => state.userInfo?.role === 'guest',
    isAdmin: (state) => state.userInfo?.role === 'admin',
  },
  actions: {
    setToken(token: string) {
      this.token = token
      localStorage.setItem('token', token)
    },
    setRefreshToken(refreshToken: string) {
      this.refreshToken = refreshToken
      localStorage.setItem('refresh_token', refreshToken)
    },
    getRefreshToken() {
      return this.storedRefreshToken
    },
    setUserId(userId: string) {
      this.userId = userId
      localStorage.setItem('user_id', userId)
    },

    setDeviceId() {
      this.deviceId = getDeviceId()
      localStorage.setItem('device_id', this.deviceId)
      // @ts-ignore
      if (window.collectEvent) {
        // @ts-ignore
        window.collectEvent('config', {
          user_device_id: this.deviceId,
        })
      }
    },

    setUserInfo(userInfo: UserState['userInfo']) {
      this.userInfo = userInfo
      this.isAuthenticated = !!userInfo
      localStorage.setItem('userInfo', JSON.stringify(userInfo))

      // 设置 Sentry 用户信息
      if (userInfo) {
        setSentryUser({
          id: userInfo.uuid,
          email: userInfo.email,
          username: userInfo.name,
        })

        // 设置用户上下文
        setSentryContext('user', {
          role: userInfo.role,
          plan: userInfo.plan,
          coins: userInfo.coins,
          gender: userInfo.gender,
          status: userInfo.status,
          create_time: userInfo.create_time,
        })
      }

      // @ts-ignore
      if (window.collectEvent) {
        const isNewUser =
          new Date(userInfo.create_time).getTime() >
          Date.now() - 24 * 60 * 60 * 1000
        // @ts-ignore
        window.collectEvent('config', {
          user_unique_id: userInfo.uuid,
          reelplay_user_role: userInfo.role,
          reelplay_is_new_user: isNewUser,
        })
      }
    },

    // 添加请求缓存和防重复调用机制
    _getUserInfoPromise: null as Promise<any> | null,
    _lastGetUserInfoTime: 0,
    _getUserInfoCacheTime: 5000, // 5秒内不重复请求

    async getUserInfo(forceRefresh = false) {
      const now = Date.now()

      // 如果不是强制刷新且在缓存时间内，直接返回现有信息
      if (
        !forceRefresh &&
        now - this._lastGetUserInfoTime < this._getUserInfoCacheTime
      ) {
        console.log('🚀 CSR应用: 使用缓存的用户信息，避免重复请求')
        return this.userInfo
      }

      // 如果已有正在进行的请求，返回该Promise
      if (this._getUserInfoPromise) {
        console.log('🔄 CSR应用: 等待正在进行的用户信息请求')
        return this._getUserInfoPromise
      }

      // 创建新的请求Promise
      this._getUserInfoPromise = this._fetchUserInfo()

      try {
        const result = await this._getUserInfoPromise
        this._lastGetUserInfoTime = now
        return result
      } finally {
        // 清除Promise引用，允许下次请求
        this._getUserInfoPromise = null
      }
    },

    async _fetchUserInfo() {
      try {
        console.log('📡 CSR应用: 发起用户信息请求')
        const res = await whoami()
        if (!res.data.isOk) {
          // 如果请求失败，保留现有用户信息，避免界面显示异常
          console.warn('Failed to get user info:', res.data.message)
          return this.userInfo
        }
        const { user } = res.data.data
        this.setUserInfo({
          ...pick(user, [
            'uuid',
            'name',
            'email',
            'avatar_url',
            'status',
            'plan',
            'coins',
            'gender',
            'role',
            'create_time',
          ]),
        })
        console.log('✅ CSR应用: 用户信息更新成功，钻石数量:', user.coins || 0)
        return user
      } catch (error) {
        // 网络错误或其他异常，保留现有用户信息
        console.warn('Network error when getting user info:', error)
        return this.userInfo
      }
    },

    // 访客注册
    async signUpAsGuest() {
      try {
        this.setDeviceId()

        const gender = localStorage.getItem('user_gender')
        if (!gender) {
          console.error('No gender found in localStorage')
          return false
        }

        const res = await guestSignUp(this.deviceId, gender)
        if (!res.data.isOk) {
          console.error('Guest signup failed:', res.data.message)
          // 显示错误消息给用户
          Message.error(res.data.message)
          return false
        }

        const { auth, user } = res.data.data
        if (!auth?.access_token || !user?.uuid) {
          console.error('Invalid response data from guest signup')
          return false
        }

        this.setUserId(user.uuid)
        this.setToken(auth.access_token)
        this.setRefreshToken(auth.refresh_token)
        this.setUserInfo({
          ...pick(user, [
            'uuid',
            'name',
            'email',
            'avatar_url',
            'status',
            'plan',
            'coins',
            'gender',
            'role',
            'create_time',
          ]),
        })
        this.isAuthenticated = true

        console.log('Guest signup successful')
        return true
      } catch (error) {
        console.error('Guest signup error:', error)
        // 网络错误或其他异常，不显示错误消息，避免用户体验问题
        return false
      }
    },

    // 转换为正式用户
    async convertToUser(data: UserConvertParams) {
      try {
        await convertGuestToUser(data)
        this.setUserInfo({
          ...this.userInfo,
          name: data.name,
          email: data.email,
        })
      } catch (error) {
        console.error('Convert to user failed:', error)
        throw error
      }
    },

    // 清除用户状态
    clearUserState() {
      this.userId = null
      this.token = null
      this.userInfo = null
      this.isAuthenticated = false
      localStorage.removeItem('token')
      localStorage.removeItem('user_id')
      localStorage.removeItem('userInfo')
      localStorage.removeItem('device_id')

      // 清除 Sentry 用户信息
      setSentryUser({})
    },
    async loginWithCode(data: LoginWithCodeParams) {
      const response = await loginWithCode(data)
      if (!response.data.isOk) {
        Message.error(response.data.message)
        return false
      }
      const { auth, user, is_first_login } = response.data.data
      if (is_first_login) {
        reportEvent(ReportEvent.CompleteRegistration, {
          userId: user.uuid,
        })
      }
      this.setToken(auth.access_token)
      this.setRefreshToken(auth.refresh_token)
      this.setUserInfo(user)
      this.isAuthenticated = true
      if (window.collectEvent) {
        // @ts-ignore
        window.collectEvent('config', {
          user_unique_id: user.uuid,
        })
      }
      return true
    },
    async login(credentials: { email: string; password: string }) {
      const response = await axios.post('/api/v1/user.sign-in', {
        ...credentials,
        user_id: this.userId,
      })
      if (!response.data.isOk) {
        Message.error(response.data.message)
        return false
      }
      const { auth, user } = response.data.data
      // 保存 token 和用户信息
      this.setToken(auth.access_token)
      this.setRefreshToken(auth.refresh_token)
      this.setUserInfo(user)
      this.isAuthenticated = true

      return true
    },
    logout() {
      this.clearUserState()
      // @ts-ignore
      if (window.collectEvent) {
        // @ts-ignore
        window.collectEvent('logout')
      }
    },
    async register(data: { email: string; name: string; password: string }) {
      const gender = localStorage.getItem('user_gender')
      if (!gender) {
        localStorage.setItem('user_gender', 'unknown')
      }

      const response = await convertGuestToUser({
        user_id: this.userId,
        name: data.name,
        email: data.email,
        password: data.password,
        gender: gender,
      })
      if (!response.data.isOk) {
        Message.error(response.data.message)
        return false
      }
      const { user } = response.data.data
      if (!user.uuid) {
        Message.error('Failed to sign up')
        return false
      }
      this.setUserId(user.uuid)
      this.setUserInfo({
        ...user,
        gender: user.gender,
      })
      this.isAuthenticated = true
      return true
    },
    async handleRefreshToken(refreshToken: string) {
      const response = await axios.post('/api/v1/user.refresh', {
        refresh_token: refreshToken,
      })
      if (!response.data.isOk) {
        return false
      }
      const { auth, user } = response.data.data
      this.setToken(auth.access_token)
      this.setRefreshToken(auth.refresh_token)
      this.setUserInfo(user)
      return true
    },
    async handleSocialLogin(data: {
      auth: { access_token: string; refresh_token: string }
      user: any
      is_first_login: boolean
    }) {
      const { auth, user, is_first_login } = data
      if (is_first_login) {
        reportEvent(ReportEvent.CompleteRegistration, {
          userId: user.uuid,
        })
      }
      // Facebook Pixel tracking with retry
      const tryFacebookPixel = (retries = 0, maxRetries = 20) => {
        if (window.fbq && is_first_login) {
          window.fbq('track', 'CompleteRegistration', {
            userId: user.uuid,
          })
        } else if (is_first_login && retries < maxRetries) {
          // Retry after 200ms, up to maxRetries times (4 seconds total)
          setTimeout(() => tryFacebookPixel(retries + 1, maxRetries), 200)
        }
      }
      tryFacebookPixel()

      this.setUserId(user.uuid)
      this.setToken(auth.access_token)
      this.setRefreshToken(auth.refresh_token)
      this.setUserInfo({
        ...pick(user, [
          'uuid',
          'name',
          'email',
          'avatar_url',
          'status',
          'plan',
          'coins',
          'gender',
          'role',
          'create_time',
        ]),
      })
      // @ts-ignore
      if (window.collectEvent) {
        // @ts-ignore
        window.collectEvent('config', {
          user_unique_id: user.uuid,
        })
      }
      return true
    },
    async getUserPlayedStories() {
      const response = await userPlayedStories()
      if (!response.data.isOk) {
        Message.error(response.data.message)
        return []
      }
      const { data } = response.data
      this.userPlayedStories = data.stories
    },
    async getUserLikedStories() {
      const response = await userLikedStories()
      if (!response.data.isOk) {
        Message.error(response.data.message)
        return []
      }
      const { data } = response.data
      this.userLikedStories = data.stories
    },
    async handleUpdateUserInfo(data: Partial<UserInfo>) {
      if (this.userInfo) {
        const res = await updateUserInfo(data)
        if (res.data.isOk) {
          this.setUserInfo({
            ...this.userInfo,
            ...data,
          })
        }
      }
    },
  },
  persist: false,
})
