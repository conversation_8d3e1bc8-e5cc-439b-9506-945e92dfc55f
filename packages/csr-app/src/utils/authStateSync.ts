/**
 * 认证状态同步工具
 * 用于测试和调试状态同步功能
 */

import { useUserStore } from '@/store/user'

export interface AuthSyncTestResult {
  success: boolean
  message: string
  data?: any
  error?: string
}

/**
 * 测试用户状态同步功能
 */
export function testUserStateSync(): AuthSyncTestResult {
  try {
    const userStore = useUserStore()
    
    // 检查用户信息是否存在
    if (!userStore.userInfo) {
      return {
        success: false,
        message: '用户信息不存在',
        error: 'No user info found'
      }
    }

    // 检查钻石信息
    const coins = userStore.userInfo.coins || 0
    
    return {
      success: true,
      message: `用户状态同步测试成功，钻石数量: ${coins}`,
      data: {
        userId: userStore.userInfo.uuid,
        coins: coins,
        isAuthenticated: userStore.isAuthenticated,
        userRole: userStore.userInfo.role
      }
    }
  } catch (error) {
    return {
      success: false,
      message: '用户状态同步测试失败',
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

/**
 * 手动触发用户数据刷新
 */
export async function refreshUserData(): Promise<AuthSyncTestResult> {
  try {
    const userStore = useUserStore()
    
    console.log('🔄 开始刷新用户数据...')
    
    const userInfo = await userStore.getUserInfo()
    
    if (userInfo) {
      console.log('✅ 用户数据刷新成功:', {
        coins: userInfo.coins || 0,
        role: userInfo.role
      })
      
      return {
        success: true,
        message: `用户数据刷新成功，钻石数量: ${userInfo.coins || 0}`,
        data: userInfo
      }
    } else {
      return {
        success: false,
        message: '用户数据刷新失败',
        error: 'Failed to get user info'
      }
    }
  } catch (error) {
    console.error('❌ 用户数据刷新失败:', error)
    return {
      success: false,
      message: '用户数据刷新失败',
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

/**
 * 模拟接收父应用的状态同步消息
 */
export function simulateParentStateSync(userData: any): AuthSyncTestResult {
  try {
    const userStore = useUserStore()
    
    console.log('🧪 模拟接收父应用状态同步:', userData)
    
    // 更新localStorage
    if (userData.user) {
      localStorage.setItem('userInfo', JSON.stringify(userData.user))
      
      // 更新store
      userStore.setUserInfo(userData.user)
      
      console.log('✅ 模拟状态同步完成，钻石数量:', userData.user.coins || 0)
      
      return {
        success: true,
        message: `模拟状态同步成功，钻石数量: ${userData.user.coins || 0}`,
        data: userData.user
      }
    }
    
    return {
      success: false,
      message: '模拟状态同步失败：无用户数据',
      error: 'No user data provided'
    }
  } catch (error) {
    console.error('❌ 模拟状态同步失败:', error)
    return {
      success: false,
      message: '模拟状态同步失败',
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

/**
 * 检查localStorage中的用户数据
 */
export function checkLocalStorageUserData(): AuthSyncTestResult {
  try {
    const userInfoStr = localStorage.getItem('userInfo')
    
    if (!userInfoStr) {
      return {
        success: false,
        message: 'localStorage中没有用户数据',
        error: 'No user data in localStorage'
      }
    }
    
    const userInfo = JSON.parse(userInfoStr)
    
    return {
      success: true,
      message: `localStorage用户数据检查成功，钻石数量: ${userInfo.coins || 0}`,
      data: userInfo
    }
  } catch (error) {
    return {
      success: false,
      message: 'localStorage用户数据检查失败',
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

// 在开发环境中暴露到全局对象，方便调试
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).authSyncDebug = {
    testUserStateSync,
    refreshUserData,
    simulateParentStateSync,
    checkLocalStorageUserData
  }
  
  console.log('🔧 认证状态同步调试工具已加载到 window.authSyncDebug')
}
