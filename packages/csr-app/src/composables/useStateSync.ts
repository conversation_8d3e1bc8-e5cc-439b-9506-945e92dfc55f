/**
 * 状态同步组合式函数
 * 用于在微前端环境中同步关键状态
 */

import { onMounted, onUnmounted } from 'vue'
import {
  syncAuthState,
  syncUserState,
  isInMicroFrontend,
  sendMessageToParent,
} from '@/utils/iframeNavigation'
import { getDeploymentConfig } from '@/config/deployment'
import { useStoryStore } from '@/store/story'
import { useUserStore } from '@/store/user'
import { storeToRefs } from 'pinia'

export function useStateSync() {
  const storyStore = useStoryStore()
  const { currentStory, currentActor } = storeToRefs(storyStore)

  /**
   * 同步当前认证状态到主应用
   */
  const syncCurrentAuthState = () => {
    if (!isInMicroFrontend()) return

    const authData = {
      token: localStorage.getItem('token'),
      refreshToken: localStorage.getItem('refreshToken'),
      userId: localStorage.getItem('userId'),
      sessionId: localStorage.getItem('sessionId'),
    }

    // 只有当有实际数据时才同步
    if (authData.token || authData.userId) {
      syncAuthState(authData)
      console.log('🔐 CSR应用: 已同步认证状态到主应用')
    }
  }

  /**
   * 同步当前用户状态到主应用
   */
  const syncCurrentUserState = () => {
    if (!isInMicroFrontend()) return

    const userData = {
      user: localStorage.getItem('userInfo')
        ? JSON.parse(localStorage.getItem('userInfo')!)
        : null,
      language: localStorage.getItem('language'),
      theme: localStorage.getItem('theme'),
      userPreferences: localStorage.getItem('userPreferences')
        ? JSON.parse(localStorage.getItem('userPreferences')!)
        : null,
    }

    // 只有当有实际数据时才同步
    if (userData.user || userData.language || userData.theme) {
      syncUserState(userData)
      console.log('👤 CSR应用: 已同步用户状态到主应用')
    }
  }

  /**
   * 监听localStorage变化并自动同步
   */
  const handleStorageChange = (event: StorageEvent) => {
    if (!isInMicroFrontend()) return

    const { key, newValue } = event

    // 认证相关的key变化时同步认证状态
    if (['token', 'refreshToken', 'userId', 'sessionId'].includes(key || '')) {
      console.log('🔄 检测到认证状态变化:', key, newValue)
      setTimeout(syncCurrentAuthState, 100)
    }

    // 用户相关的key变化时同步用户状态
    if (
      ['userInfo', 'language', 'theme', 'userPreferences'].includes(key || '')
    ) {
      console.log('🔄 检测到用户状态变化:', key, newValue)
      setTimeout(syncCurrentUserState, 100)
    }
  }

  /**
   * 同步当前 story 和 actor 状态到主应用
   */
  const syncCurrentStoryState = () => {
    if (!isInMicroFrontend()) return

    const storyData = {
      currentStory: currentStory.value,
      currentActor: currentActor.value,
    }

    // 只有当有实际数据时才同步
    if (storyData.currentStory || storyData.currentActor) {
      sendMessageToParent('SYNC_STORY_STATE', storyData)
      console.log('📚 CSR应用: 已同步故事状态到主应用')
    }
  }

  /**
   * 从路由参数恢复 story 和 actor 状态
   */
  const restoreStoryFromRoute = () => {
    if (!isInMicroFrontend()) return

    const currentPath = window.location.pathname
    const params = currentPath.split('/')

    if (params.length >= 4) {
      const chatType = params[1]
      let storyId = ''
      let actorId = ''

      if (chatType === 'chat3') {
        // chat3 的参数顺序是 /chat3/:characterId/:storyId
        actorId = params[2]
        storyId = params[3]
      } else {
        // 其他的参数顺序是 /chatX/:storyId/:characterId
        storyId = params[2]
        actorId = params[3]
      }

      // 向主应用请求故事和角色数据
      if (storyId && actorId) {
        sendMessageToParent('REQUEST_STORY_DATA', { storyId, actorId })
        console.log('📚 CSR应用: 向主应用请求故事数据', { storyId, actorId })
      }
    }
  }

  /**
   * 处理来自主应用的状态同步
   */
  const handleParentStateSync = (event: MessageEvent) => {
    // 确保消息来自主应用
    const config = getDeploymentConfig()
    if (event.origin !== config.mainAppUrl) return

    const { type, payload, data } = event.data

    // 支持多种消息类型以确保兼容性
    if (
      type === 'STATE_SYNC' ||
      type === 'AUTH_STATE_SYNC' ||
      type === 'USER_STATE_SYNC'
    ) {
      console.log('📨 CSR应用: 收到主应用状态同步', type, payload || data)

      const syncData = payload || data

      // 同步认证状态
      if (syncData.auth || (type === 'AUTH_STATE_SYNC' && syncData)) {
        const authData = syncData.auth || syncData
        const { token, refreshToken, userId, sessionId } = authData
        if (token) localStorage.setItem('token', token)
        if (refreshToken) localStorage.setItem('refreshToken', refreshToken)
        if (userId) localStorage.setItem('userId', userId)
        if (sessionId) localStorage.setItem('sessionId', sessionId)

        console.log('🔐 CSR应用: 认证状态已同步')
      }

      // 同步用户状态
      if (syncData.user || (type === 'USER_STATE_SYNC' && syncData)) {
        const userData = syncData.user || syncData
        const { user, language, theme, userPreferences } = userData

        if (user) {
          localStorage.setItem('userInfo', JSON.stringify(user))

          // 重要：同时更新userStore以确保界面响应式更新
          const userStore = useUserStore()
          userStore.setUserInfo(user)

          console.log('👤 CSR应用: 用户信息已同步，钻石数量:', user.coins || 0)

          // 只有在用户信息看起来不完整时才主动刷新
          if (!user.coins && user.coins !== 0) {
            setTimeout(() => {
              userStore
                .getUserInfo(true) // 强制刷新
                .then(() => {
                  console.log('🔄 CSR应用: 用户数据已刷新（因为钻石信息缺失）')
                })
                .catch((error) => {
                  console.warn('⚠️ CSR应用: 用户数据刷新失败:', error)
                })
            }, 500)
          } else {
            console.log('✅ CSR应用: 用户信息完整，跳过额外刷新')
          }
        }

        if (language) localStorage.setItem('language', language)
        if (theme) localStorage.setItem('theme', theme)
        if (userPreferences)
          localStorage.setItem(
            'userPreferences',
            JSON.stringify(userPreferences),
          )
      }

      console.log('✅ CSR应用: 状态同步完成')
    } else if (type === 'STORY_DATA_RESPONSE') {
      console.log('📨 CSR应用: 收到主应用故事数据响应', payload)

      if (payload.story) {
        storyStore.setCurrentStory(payload.story)
        console.log('📚 CSR应用: 已设置当前故事', payload.story)
      }
      if (payload.actor) {
        storyStore.setCurrentActor(payload.actor)
        console.log('👤 CSR应用: 已设置当前角色', payload.actor)
      }
    }
  }

  // 组件挂载时设置监听器
  onMounted(() => {
    if (typeof window === 'undefined') return

    // 监听localStorage变化
    window.addEventListener('storage', handleStorageChange)

    // 监听来自主应用的消息
    window.addEventListener('message', handleParentStateSync)

    // 初始同步当前状态
    setTimeout(() => {
      syncCurrentAuthState()
      syncCurrentUserState()
      syncCurrentStoryState()
      // 如果没有当前故事或角色，尝试从路由恢复
      if (!currentStory.value || !currentActor.value) {
        restoreStoryFromRoute()
      }
    }, 500)
  })

  // 组件卸载时清理监听器
  onUnmounted(() => {
    if (typeof window === 'undefined') return

    window.removeEventListener('storage', handleStorageChange)
    window.removeEventListener('message', handleParentStateSync)
  })

  return {
    syncCurrentAuthState,
    syncCurrentUserState,
    syncCurrentStoryState,
    restoreStoryFromRoute,
    isInMicroFrontend: isInMicroFrontend(),
  }
}
